<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="仪器设备采购计划" okText="确认" @ok="handleSubmit" :width="1400">
    <div class="table-container">
      <div class="table-header">
        <div class="table-actions">
          <button class="action-btn" @click="printTable">打印</button>
        </div>
      </div>
      <table :id="printId" border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
        <thead>
          <tr>
            <th style="text-align: center; font-size: 16px; font-weight: bold; padding: 15px;" colspan="5">仪器设备采购计划</th>
          </tr>
        </thead>
        <tbody>
          <!-- 申请设备名称 -->
          <tr>
            <td style="width: 15%; text-align: left; padding: 8px; background-color: #f5f5f5;">申请设备名称</td>
            <td colspan="4" style="text-align: left; padding: 8px;">{{ formData.equipmentName || '' }}</td>
          </tr>

          <!-- 生产厂家 -->
          <tr>
            <td style="width: 15%; text-align: left; padding: 8px; background-color: #f5f5f5;">生产厂家</td>
            <td colspan="4" style="text-align: left; padding: 8px;">{{ formData.manufacturer || '' }}</td>
          </tr>

          <!-- 型号规格、单位数量、预计金额 -->
          <tr>
            <td style="width: 15%; text-align: left; padding: 8px; background-color: #f5f5f5;">型号或规格</td>
            <td style="width: 25%; text-align: left; padding: 8px;">{{ formData.modelSpec || '' }}</td>
            <td style="width: 15%; text-align: left; padding: 8px; background-color: #f5f5f5;">单位及数量</td>
            <td style="width: 20%; text-align: left; padding: 8px;">{{ formData.unitQuantity || '' }}</td>
            <td style="width: 25%; text-align: left; padding: 8px;">
              <span style="background-color: #f5f5f5; padding: 2px 5px;">预计金额(元)</span>
              {{ formData.estimatedAmount || '' }}
            </td>
          </tr>
          <tr style="height: 12rem;text-align: left;">
            <td colspan="6" style="text-align: left;">
              申请用途及原因:{{ formData.unitQuantity }}
              <br>
              <br>
              <br>
              技术指标:{{ formData.unitQuantity }}
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">申请人签字:{{ formData.commitPerson_dictText }}</a-col>
                <a-col :span="12">日期:{{ formData.commitTime }}</a-col>
              </a-row>
            </td>
          </tr>
          <tr style="height: 6rem;text-align: left;">
            <td colspan="6" style="text-align: left;">
              技术负责人审批意见:{{ formData.unitQuantity }}
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">技术负责人签字:{{ formData.commitPerson_dictText }}</a-col>
                <a-col :span="12">日期:{{ formData.commitTime }}</a-col>
              </a-row>
            </td>
          </tr>
          <tr style="height: 6rem;text-align: left;">
            <td colspan="6" style="text-align: left;">
              实验室审批意见:{{ formData.unitQuantity }}
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">实验室经理签字:{{ formData.commitPerson_dictText }}</a-col>
                <a-col :span="12">日期:{{ formData.commitTime }}</a-col>
              </a-row>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);

// 定义表单数据接口
interface FormDataType {
  // 基本信息
  equipmentName: string;           // 申请设备名称
  manufacturer: string;            // 生产厂家
  modelSpec: string;              // 型号或规格
  unitQuantity: string;           // 单位及数量
  estimatedAmount: string;        // 预计金额(元)
  applicationReason: string;      // 申请原因及用途
  technicalSpecs: string;         // 技术指标

  // 申请人签字信息
  applicantSignature: string;     // 申请人签字
  applicationDate: string;        // 申请年
  applicationMonth: string;       // 申请月
  applicationDay: string;         // 申请日

  // 技术负责人签字信息
  techManagerSignature: string;   // 技术负责人签字
  techManagerDate: string;        // 技术负责人年
  techManagerMonth: string;       // 技术负责人月
  techManagerDay: string;         // 技术负责人日

  // 实验室主任签字信息
  labDirectorSignature: string;   // 实验室主任签字
  labDirectorDate: string;        // 实验室主任年
  labDirectorMonth: string;       // 实验室主任月
  labDirectorDay: string;         // 实验室主任日

  // 实验室负责人签字信息
  labManagerSignature: string;    // 实验室负责人签字
  labManagerDate: string;         // 实验室负责人年
  labManagerMonth: string;        // 实验室负责人月
  labManagerDay: string;          // 实验室负责人日
}

// 定义表单数据
const formData = reactive<FormDataType>({
  // 基本信息
  equipmentName: '',
  manufacturer: '',
  modelSpec: '',
  unitQuantity: '',
  estimatedAmount: '',
  applicationReason: '',
  technicalSpecs: '',

  // 申请人签字信息
  applicantSignature: '',
  applicationDate: '',
  applicationMonth: '',
  applicationDay: '',

  // 技术负责人签字信息
  techManagerSignature: '',
  techManagerDate: '',
  techManagerMonth: '',
  techManagerDay: '',

  // 实验室主任签字信息
  labDirectorSignature: '',
  labDirectorDate: '',
  labDirectorMonth: '',
  labDirectorDay: '',

  // 实验室负责人签字信息
  labManagerSignature: '',
  labManagerDate: '',
  labManagerMonth: '',
  labManagerDay: '',
});

// 重置表单数据
const resetFormData = () => {
  // 基本信息
  formData.equipmentName = '';
  formData.manufacturer = '';
  formData.modelSpec = '';
  formData.unitQuantity = '';
  formData.estimatedAmount = '';
  formData.applicationReason = '';
  formData.technicalSpecs = '';

  // 申请人签字信息
  formData.applicantSignature = '';
  formData.applicationDate = '';
  formData.applicationMonth = '';
  formData.applicationDay = '';

  // 技术负责人签字信息
  formData.techManagerSignature = '';
  formData.techManagerDate = '';
  formData.techManagerMonth = '';
  formData.techManagerDay = '';

  // 实验室主任签字信息
  formData.labDirectorSignature = '';
  formData.labDirectorDate = '';
  formData.labDirectorMonth = '';
  formData.labDirectorDay = '';

  // 实验室负责人签字信息
  formData.labManagerSignature = '';
  formData.labManagerDate = '';
  formData.labManagerMonth = '';
  formData.labManagerDay = '';
};

//表单赋值
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  console.log("🚀 ~ data:", data)
  printId.value = buildUUID().toString();

  // 先重置表单数据
  resetFormData();

  if (data && data.record) {
    // 如果有传入数据，则赋值
    // 基本信息
    formData.equipmentName = data.record.equipmentName || '';
    formData.manufacturer = data.record.manufacturer || '';
    formData.modelSpec = data.record.modelSpec || '';
    formData.unitQuantity = data.record.unitQuantity || '';
    formData.estimatedAmount = data.record.estimatedAmount || '';
    formData.applicationReason = data.record.applicationReason || '';
    formData.technicalSpecs = data.record.technicalSpecs || '';

    // 申请人签字信息
    formData.applicantSignature = data.record.applicantSignature || '';
    formData.applicationDate = data.record.applicationDate || '';
    formData.applicationMonth = data.record.applicationMonth || '';
    formData.applicationDay = data.record.applicationDay || '';

    // 技术负责人签字信息
    formData.techManagerSignature = data.record.techManagerSignature || '';
    formData.techManagerDate = data.record.techManagerDate || '';
    formData.techManagerMonth = data.record.techManagerMonth || '';
    formData.techManagerDay = data.record.techManagerDay || '';

    // 实验室主任签字信息
    formData.labDirectorSignature = data.record.labDirectorSignature || '';
    formData.labDirectorDate = data.record.labDirectorDate || '';
    formData.labDirectorMonth = data.record.labDirectorMonth || '';
    formData.labDirectorDay = data.record.labDirectorDay || '';

    // 实验室负责人签字信息
    formData.labManagerSignature = data.record.labManagerSignature || '';
    formData.labManagerDate = data.record.labManagerDate || '';
    formData.labManagerMonth = data.record.labManagerMonth || '';
    formData.labManagerDay = data.record.labManagerDay || '';
  }
});

// 打印表格
function printTable() {
  printJS({
    type: 'html',
    printable: printId.value,
    scanStyles: false,
  });
}

async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}


.action-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

table {
  border: 1px solid #ccc;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 8px;
  height: 40px;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
