<template>
  <!--  :rowSelection="{ type: 'radio', selectedRowKeys: checkedKeys, onChange: onSelectChange }" -->
  <BasicTable @register="registerTable" :rowSelection="rowSelection">
    <template #tableTitle>
      <!-- v-if="hasPermission('sampleManagement:handleScan')" -->
      <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleAdd">新增</a-button>
      <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
      <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls">导出</a-button>
      <a-button type="primary" @click="handleSelectRecords">记录查询</a-button>
      <a-button type="primary" @click="handleWorkersSetUp">人员设置</a-button>

      <a-dropdown v-if="selectedRowKeys.length > 0">
        <template #overlay>
          <a-menu>
            <a-menu-item key="1" @click="batchCode">
              <!-- <Icon icon="ant-design:delete-outlined"></Icon> -->
              批量二维码
            </a-menu-item>
            <a-menu-item key="2" @click="batchCalibration">
              <!-- <Icon icon="ant-design:delete-outlined"></Icon> -->
              批量校准
            </a-menu-item>
            <a-menu-item key="3" @click="batchMaintenance">
              <!-- <Icon icon="ant-design:delete-outlined"></Icon> -->
              批量维修
            </a-menu-item>
          </a-menu>
        </template>
        <a-button>批量操作
          <Icon icon="mdi:chevron-down"></Icon>
        </a-button>
      </a-dropdown>
    </template>
    <!-- 名称 -->
    <template #reaName="{ record }">
      <span>{{ record.instrumentName }}</span>
      &nbsp;<a-space v-if="dayjs(record.lastCalibrationDate,'YYYY-MM-DD').add(record.calibrationPeriod,'month').diff(dayjs(),'day')<= 30" style="color:orange;font-size: 20px;"><clock-circle-filled style="color:orange;" /></a-space>
    </template>
    <!--操作栏-->
    <template #action="{ record }">
      <div v-if="record.status == '2'">
        <TableAction
          :actions="[
            {
              label: '校准恢复',
              onClick: handleRenewCalibration.bind(null, record),
              // auth: 'other', // 根据权限控制是否显示: 无权限，不显示
            },
          ]"
        />
      </div>
      <div v-if="record.status == '3'">
        <TableAction
          :actions="[
            {
              label: '维修恢复',
              onClick: handleRenewRepair.bind(null, record),
              // auth: 'other', // 根据权限控制是否显示: 无权限，不显示
            },
          ]"
        />
      </div>
      <div v-if="record.status == '1'">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
        <!-- <TableAction
          :actions="[
            
            
          ]"
        /> -->
      </div>
    </template>
  </BasicTable>

  <!-- 表单区域 -->
  <instrumentManagementModel @register="instrumentManagementModelRegister" @success="handleSuccess"></instrumentManagementModel>
  <workersSetUpModel @register="workersSetUpModelRegister" @success="handleSuccess"></workersSetUpModel>
  <repairModel @register="repairModelRegister" @success="handleSuccess"></repairModel>
  <calibrationMoldel @register="calibrationMoldelRegister" @success="handleSuccess"></calibrationMoldel>
  <scrapModel @register="scrapModelRegister" @success="handleSuccess"></scrapModel>
  <searchRecordModel @register="searchRecordModelRegister" @success="handleSuccess"></searchRecordModel>
  <instrumentQrCodeModal @register="codeRegister"></instrumentQrCodeModal>
  <acceptModal @register="acceptReg" @success="handleSuccess"></acceptModal>
  <acceptRecordModal @register="acceptRecordReg"></acceptRecordModal>
  <checkModal @register="checkReg" @success="handleSuccess"></checkModal>
  <maintenanceModal @register="maintenanceReg" @success="handleSuccess"></maintenanceModal>
  <procurementModal @register="regProcurementModal" @success="handleSuccess"></procurementModal>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem, EditRecordRow } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import { searchFormSchema } from './instrumentManagement.data';
  import { list, deleteOne, getImportUrl, getExportUrl, deleteRecord, getPlaceTree, getAcceptRecord, getPlanList, getMaintentanceList } from './instrumentManagement.api';
  import { usePermission } from '/@/hooks/web/usePermission';
  import instrumentManagementModel from './components/instrumentManagementModel.vue';
  import workersSetUpModel from './components/workersSetUpModel.vue';
  import searchRecordModel from './components/searchRecordModel.vue';
  import repairModel from './components/repairModel.vue';
  import calibrationMoldel from './components/calibrationMoldel.vue';
  import scrapModel from './components/scrapModel.vue';
  import instrumentQrCodeModal from './components/instrumentQrCodeModal.vue';
  import acceptModal from './components/acceptModal.vue';
  import acceptRecordModal from './components/acceptRecordModal.vue';
  import checkModal from './components/checkModal.vue';
  import maintenanceModal from './components/maintenanceModal.vue';
  import procurementModal from './components/procurementModal.vue';
  import { defHttp1 } from '/@/utils/http/axios/index1';
  import dayjs from 'dayjs';
  import { ExclamationCircleFilled,ClockCircleFilled } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  // 权限控制
  const { hasPermission } = usePermission();
  const [instrumentManagementModelRegister, { openModal: openInstrumentManagementModel }] = useModal();
  const [workersSetUpModelRegister, { openModal: openWorkersSetUpModel }] = useModal();
  const [repairModelRegister, { openModal: openrepairModel }] = useModal();
  const [calibrationMoldelRegister, { openModal: opencalibrationMoldel }] = useModal();
  const [scrapModelRegister, { openModal: openscrapModel }] = useModal();
  const [searchRecordModelRegister, { openModal: opensearchRecordModel }] = useModal();
  const [codeRegister, { openModal: openCodeModel }] = useModal();
  const [acceptReg, { openModal: openAcceptModel }] = useModal();
  const [acceptRecordReg, { openModal: openAcceptRecordModel }] = useModal();
  const [checkReg, { openModal: openCheckModel }] = useModal();
  const [maintenanceReg, { openModal: openMaintenanceModel }] = useModal();
  const [regProcurementModal, { openModal: openProcurementModal }] = useModal();
  const checkedKeys = ref<Array<string | number>>([]);
  const columns: BasicColumn[] = [
    {
      title: '仪器编号',
      dataIndex: 'instrumentNo',
    },
    {
      title: '仪器名称',
      dataIndex: 'instrumentName',
      slots: { customRender: 'reaName' }
    },
    {
      title: '仪器型号',
      dataIndex: 'instrumentModel',
    },
    {
      title: '状态',
      dataIndex: 'status',
      customRender: ({ text }) => {
        if (text == '1') {
          return '正常使用';
        } else if (text == '2') {
          return '校准';
        } else if (text == '3') {
          return '维修';
        } else if (text == '4') {
          return '报废中';
        } else if (text == '5') {
          return '已报废';
        } else if (text == '6') {
          return '已删除';
        } else {
          return '';
        }
      },
    }
  ]
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '仪器管理',
      api: list,
      rowKey: 'id',
      columns,
      canResize: false,
      formConfig: {
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 300,
        fixed: 'left',
        title: '编辑',
        dataIndex: 'action',
        slots: { customRender: 'action' },
      },
    },
    exportConfig: {
      name: '仪器列表',
      url: getExportUrl,
    },
    importConfig: {
      url: getImportUrl,
      success: () => reload(),
    },
  });
  const [registerTable, { reload,getSelectRows,getSelectRowKeys,clearSelectedRowKeys }, { rowSelection, selectedRowKeys }] = tableContext;
  /**
   * 维护事件
   */
  function handleAdd() {
    getPlaceTree().then((res) => {
      openInstrumentManagementModel(true, {
        res,
        isUpdate: false,
        showFooter: true,
      });
    })
    
  }

  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    let sss = dayjs(record.lastCalibrationDate,'YYYY-MM-DD').add(record.calibrationPeriod,'month').diff(dayjs(),'day')
    console.log("🚀 ~ file: instrumentManagement.vue:224 ~ handleEdit ~ dayjs(record.lastCalibrationDate,'YYYY-MM-DD').add(record.calibrationPeriod,'month'):", dayjs(record.lastCalibrationDate,'YYYY-MM-DD').add(record.calibrationPeriod,'month'))
    console.log("🚀 ~ file: instrumentManagement.vue:223 ~ handleEdit ~ sss:", sss, typeof sss)
    getPlaceTree().then((res) => {
      
      openInstrumentManagementModel(true, {
        record,
        res,
        isUpdate: true,
        showFooter: true,
      });
    })
    
  }
  /**
   * 验收
   */
  function handleAccept(record: Recordable) {
    openAcceptModel(true, {
      record: { parentId: record.id },
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 验收记录
   */
  async function handleAcceptRecord(record: Recordable) {
    const R = await getAcceptRecord({ insId: record.id })
    console.log("🚀 ~ handleAcceptRecord ~ res:", R)
    let res = R.records[0]
    res.acceptId = res.id
    openAcceptRecordModel(true, {
      record: { res: res, instrumentDos: record },
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 核查
   */
  async function handleCheck(record: Recordable) {
    const res = await getPlanList({ insNo: record.instrumentNo })
    // let res = [{id: '11', planName: '计划1'},{id: '12', planName: '计划2'}]
    openCheckModel(true, {
      record: { res: res, instrumentDos: record },
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 维护保养
   */
  async function handleMaintentance(record: Recordable) {
    const res = await getMaintentanceList({ insId: record.id })
    // let res = [{id: '11', planName: '计划1'},{id: '12', planName: '计划2'}]
    openMaintenanceModel(true, {
      record: { res: res, instrumentDos: record },
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 采购
   */
  async function handleProcurement(record: Recordable) {
    openProcurementModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 校准事件
   */
  function handleCalibration(record: Recordable) {
    let params = {
      id: record.id,
      status: 2,
    };
    defHttp1.get({ url: '/instrument/instrumentManagement/changeStatus', params }).then((res) => {
      if (res.code == 200) {
        handleSuccess();
      }
    });
  }
  /**
   * 批量校准事件
   */
  function batchCalibration() {
    let records = getSelectRows();
    console.log("🚀 ~ file: instrumentManagement.vue:265 ~ batchCalibration ~ records:", records)
    let keys = getSelectRowKeys()
    console.log("🚀 ~ file: instrumentManagement.vue:267 ~ batchCalibration ~ keys:", keys)
    let params = [...keys];
    console.log("🚀 ~ file: instrumentManagement.vue:269 ~ batchCalibration ~ params:", params)

    for(let i = 0; i < records.length; i++) {
      if(records[i].status != '1') {
        message.warning('请确保所选仪器状态都为正常！')
        return false;
      }
    }
    defHttp1.post({ url: '/instrument/instrumentManagement/batchCalibration', params }).then((res) => {
      if (res.code == 200) {
        handleSuccess();
        clearSelectedRowKeys();
      }
    });
  }
  /**
   * 校准恢复事件
   */
  async function handleRenewCalibration(r: Recordable) {
    let params = {
      id: r.id
    }
    let record = Object.assign({},r);
    defHttp1.get({ url: '/instrument/instrumentManagement/getLastSubData', params }).then((res) => {
      console.log("🚀 ~ file: instrumentManagement.vue:292 ~ defHttp1.get ~ res:", res)
      if (res.code == 200) {
        record.lastData = res.result;
        opencalibrationMoldel(true, {
          record,
          isUpdate: true,
          showFooter: true,
        });
      }
    });
    
  }
  /**
   * 维修事件
   */
  function handleRepair(record: Recordable) {
    let params = {
      id: record.id,
      status: 3,
    };
    defHttp1.get({ url: '/instrument/instrumentManagement/changeStatus', params }).then((res) => {
      if (res.code == 200) {
        handleSuccess();
      }
    });
  }
  /**
   * 批量维修事件
   */
  function batchMaintenance() {
    let records = getSelectRows();
    console.log("🚀 ~ file: instrumentManagement.vue:265 ~ batchMaintenance ~ records:", records)
    let keys = getSelectRowKeys()
    console.log("🚀 ~ file: instrumentManagement.vue:267 ~ batchMaintenance ~ keys:", keys)
    let params = [...keys];
    console.log("🚀 ~ file: instrumentManagement.vue:269 ~ batchMaintenance ~ params:", params)

    for(let i = 0; i < records.length; i++) {
      if(records[i].status != '1') {
        message.warning('请确保所选仪器状态都为正常！')
        return false;
      }
    }
    defHttp1.post({ url: '/instrument/instrumentManagement/batchMaintenance', params }).then((res) => {
      if (res.code == 200) {
        handleSuccess();
        clearSelectedRowKeys();
      }
    });
  }
  /**
   * 维修恢复事件
   */
  async function handleRenewRepair(record: Recordable) {
    openrepairModel(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 报废事件
   */
  async function handleScrap(record: Recordable) {
    openscrapModel(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  async function handleCode(record: Recordable) {
    let records = [];
    records.push(record);
    openCodeModel(true, {
      records,
      isUpdate: true,
      showFooter: true,
    });
  }
  async function batchCode() {
    let records = getSelectRows();
    openCodeModel(true, {
      records,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 导入事件
   */
  async function handleImport(record: Recordable) {}
  /**
   * 导出事件
   */
  async function handleExport(record: Recordable) {}
  /**
   * 记录查询事件
   */
  async function handleSelectRecords() {
    opensearchRecordModel(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 人员设置事件
   */
  async function handleWorkersSetUp() {
    openWorkersSetUpModel(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  // 表格选中改变
  function onSelectChange(selectedRowKeys: (string | number)[]) {
    console.log(selectedRowKeys);
    checkedKeys.value = selectedRowKeys;
  }
  // 删除
  async function handleDel(record: Recordable) {
    await deleteRecord({ id: record.id }, () => {
      reload();
    });
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        // auth: 'other', // 根据权限控制是否显示: 无权限，不显示
      },
      {
        label: '报废',
        onClick: handleScrap.bind(null, record),
        // auth: 'other', // 根据权限控制是否显示: 无权限，不显示
      },
      {
        label: '验收',
        onClick: handleAccept.bind(null, record)
      }
    ];
  }
  function getDropDownAction(record) {
    return [
      {
        label: '校准',
        popConfirm: {
          title: '确认校准？',
          confirm: handleCalibration.bind(null, record),
        },
        // auth: 'other', // 根据权限控制是否显示: 无权限，不显示
      },
      {
        label: '维修',
        popConfirm: {
          title: '确认维修？',
          confirm: handleRepair.bind(null, record),
        },
        // auth: 'other', // 根据权限控制是否显示: 无权限，不显示
      },
      {
        label: '删除',
        popConfirm: {
          title: '确认删除？',
          confirm: handleDel.bind(null, record),
        },
      },
      {
        label: '二维码',
        onClick: handleCode.bind(null, record),
      },
      {
        label: '验收记录',
        onClick: handleAcceptRecord.bind(null, record)
      },
      {
        label: '核查',
        onClick: handleCheck.bind(null, record)
      },
      {
        label: '保养',
        onClick: handleMaintentance.bind(null, record)
      },
      {
        label: '采购',
        onClick: handleProcurement.bind(null, record)
      },
    ];
  }

</script>
